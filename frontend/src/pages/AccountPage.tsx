import React, { useState } from 'react';
import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import { motion } from 'framer-motion';
import {
  UserIcon,
  EnvelopeIcon,
  PhoneIcon,
  MapPinIcon,
  ShieldCheckIcon,
  CalendarIcon,
  PencilIcon,
  CheckIcon,
  XMarkIcon,
  CameraIcon,
  Cog6ToothIcon,
  ChartPieIcon,
  ClockIcon,
  BuildingOfficeIcon,
} from '@heroicons/react/24/outline';
import { useAuth } from '../contexts/AuthContext';
import { accountAPI } from '../services/api';
import { Avatar, Card, Button, Modal } from '../components/ui';
import toast from 'react-hot-toast';

interface UserProfile {
  id: number;
  username: string;
  email: string;
  full_name: string;
  job_title: string;
  institution: string;
  authorized_region: string;
  primary_authority: string;
  created_at: string;
  last_login: string;
}

export const AccountPage: React.FC = () => {
  const { user } = useAuth();
  const [isEditing, setIsEditing] = useState(false);
  const [showSettingsModal, setShowSettingsModal] = useState(false);
  const [formData, setFormData] = useState({
    full_name: '',
    job_title: '',
    institution: '',
  });

  const queryClient = useQueryClient();

  // Requête pour le profil utilisateur
  const { data: profile, isLoading } = useQuery({
    queryKey: ['profile'],
    queryFn: () => accountAPI.getProfile(),
    onSuccess: (data) => {
      if (data) {
        setFormData({
          full_name: data.full_name || '',
          job_title: data.job_title || '',
          institution: data.institution || '',
        });
      }
    },
  });

  // Mutation pour mettre à jour le profil
  const updateProfile = useMutation({
    mutationFn: (data: typeof formData) => accountAPI.updateProfile(data),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['profile'] });
      setIsEditing(false);
      toast.success('Profil mis à jour avec succès');
    },
    onError: () => {
      toast.error('Erreur lors de la mise à jour du profil');
    },
  });

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    updateProfile.mutate(formData);
  };

  const handleCancel = () => {
    if (profile) {
      setFormData({
        full_name: profile.full_name || '',
        job_title: profile.job_title || '',
        institution: profile.institution || '',
      });
    }
    setIsEditing(false);
  };

  if (isLoading) {
    return (
      <div className="flex justify-center items-center h-64">
        <motion.div
          animate={{ rotate: 360 }}
          transition={{ duration: 1, repeat: Infinity, ease: "linear" }}
          className="w-12 h-12 border-4 border-night-blue border-t-transparent rounded-full"
        />
      </div>
    );
  }

  return (
    <div className="space-y-8">
      {/* En-tête du profil moderne */}
      <motion.div
        initial={{ opacity: 0, y: -20 }}
        animate={{ opacity: 1, y: 0 }}
        className="bg-gradient-to-r from-night-blue to-slate-800 rounded-2xl p-8 text-white shadow-2xl"
      >
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-6">
            <div className="relative">
              <Avatar
                fallback={profile?.full_name}
                size="xl"
                online
                className="ring-4 ring-white/20"
              />
              <motion.button
                whileHover={{ scale: 1.1 }}
                whileTap={{ scale: 0.9 }}
                className="absolute -bottom-2 -right-2 w-8 h-8 bg-gold rounded-full flex items-center justify-center shadow-lg"
                onClick={() => toast.info('Changement de photo - Bientôt disponible')}
              >
                <CameraIcon className="w-4 h-4 text-night-blue" />
              </motion.button>
            </div>
            <div>
              <h1 className="text-3xl font-bold mb-2">{profile?.full_name}</h1>
              <p className="text-slate-300 text-lg mb-1">{profile?.job_title}</p>
              <div className="flex items-center text-slate-400 space-x-4">
                <span className="flex items-center">
                  <MapPinIcon className="w-4 h-4 mr-2" />
                  {profile?.authorized_region}
                </span>
                <span className="flex items-center">
                  <ShieldCheckIcon className="w-4 h-4 mr-2" />
                  {profile?.primary_authority}
                </span>
              </div>
            </div>
          </div>
          <div className="flex space-x-3">
            <Button
              variant="ghost"
              onClick={() => setShowSettingsModal(true)}
              icon={Cog6ToothIcon}
              className="text-white hover:bg-white/10"
            >
              Paramètres
            </Button>
            <Button
              variant={isEditing ? 'danger' : 'secondary'}
              onClick={isEditing ? handleCancel : () => setIsEditing(true)}
              icon={isEditing ? XMarkIcon : PencilIcon}
            >
              {isEditing ? 'Annuler' : 'Modifier'}
            </Button>
          </div>
        </div>
      </motion.div>

      <div className="card">
        <div className="flex items-start gap-6">
          <div className="flex-shrink-0">
            <UserCircleIcon className="h-24 w-24 text-gray-400" />
          </div>
          <div className="flex-1">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <div>
                <h3 className="text-lg font-medium text-night-blue mb-4">
                  Informations personnelles
                </h3>
                {isEditing ? (
                  <form onSubmit={handleSubmit} className="space-y-4">
                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-1">
                        Nom complet
                      </label>
                      <input
                        type="text"
                        className="input-field"
                        value={formData.full_name}
                        onChange={(e) => setFormData({ ...formData, full_name: e.target.value })}
                      />
                    </div>
                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-1">
                        Poste
                      </label>
                      <input
                        type="text"
                        className="input-field"
                        value={formData.job_title}
                        onChange={(e) => setFormData({ ...formData, job_title: e.target.value })}
                      />
                    </div>
                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-1">
                        Institution
                      </label>
                      <input
                        type="text"
                        className="input-field"
                        value={formData.institution}
                        onChange={(e) => setFormData({ ...formData, institution: e.target.value })}
                      />
                    </div>
                    <div className="flex gap-4">
                      <button
                        type="button"
                        onClick={() => setIsEditing(false)}
                        className="btn-secondary"
                      >
                        Annuler
                      </button>
                      <button type="submit" className="btn-primary">
                        Enregistrer
                      </button>
                    </div>
                  </form>
                ) : (
                  <div className="space-y-4">
                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-1">
                        Nom complet
                      </label>
                      <p className="text-gray-900">{profile?.full_name}</p>
                    </div>
                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-1">
                        Poste
                      </label>
                      <p className="text-gray-900">{profile?.job_title}</p>
                    </div>
                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-1">
                        Institution
                      </label>
                      <p className="text-gray-900">{profile?.institution}</p>
                    </div>
                    <button
                      onClick={() => setIsEditing(true)}
                      className="btn-primary"
                    >
                      Modifier
                    </button>
                  </div>
                )}
              </div>

              <div>
                <h3 className="text-lg font-medium text-night-blue mb-4">
                  Informations système
                </h3>
                <div className="space-y-4">
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">
                      Nom d'utilisateur
                    </label>
                    <p className="text-gray-900">{profile?.username}</p>
                  </div>
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">
                      Email
                    </label>
                    <p className="text-gray-900">{profile?.email}</p>
                  </div>
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">
                      Rôle
                    </label>
                    <p className="text-gray-900">{profile?.primary_authority}</p>
                  </div>
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">
                      Région assignée
                    </label>
                    <p className="text-gray-900">{profile?.authorized_region}</p>
                  </div>
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">
                      Dernière connexion
                    </label>
                    <p className="text-gray-900">
                      {profile?.last_login ? new Date(profile.last_login).toLocaleString() : 'N/A'}
                    </p>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};