import React from 'react';
import { useQuery } from '@tanstack/react-query';
import { motion } from 'framer-motion';
import {
  ExclamationTriangleIcon,
  ChartBarIcon,
  DocumentTextIcon,
  UsersIcon,
  ClockIcon,
  BanknotesIcon,
  ShieldCheckIcon,
  ArrowTrendingUpIcon,
  ArrowTrendingDownIcon,
  CameraIcon,
  MagnifyingGlassIcon,
  MapPinIcon,
  ChartPieIcon,
} from '@heroicons/react/24/outline';
import { useAuth } from '../contexts/AuthContext';
import { imagesAPI, detectionsAPI, alertsAPI, investigationsAPI } from '../services/api';
import statsService from '../services/stats.service';
import type { DashboardStats } from '../services/stats.service';
import { StatCard, Card } from '../components/ui';
import { <PERSON><PERSON><PERSON>, BarChart, DoughnutChart } from '../components/charts/ChartComponents';

interface StatCardData {
  title: string;
  value: number;
  icon: React.ElementType;
  color: string;
  trend?: {
    value: number;
    isPositive: boolean;
  };
}

export const DashboardPage: React.FC = () => {
  const { user, hasAuthority } = useAuth();

  // Fetch dashboard statistics
  const { data: dashboardStatsData, isLoading: isLoadingStats } = useQuery<DashboardStats, Error>({
    queryKey: ['dashboardStats'],
    queryFn: () => statsService.getDashboardStats(),
  });

  // Existing data queries - keep them for now, will remove if statsService provides all needed data
  const { data: imagesData } = useQuery({
    queryKey: ['recent-images'],
    queryFn: () => imagesAPI.getRecentImages(), // This might be replaceable by dashboardStatsData
  });

  const { data: detectionsData } = useQuery({ // This might be replaceable
    queryKey: ['recent-detections'],
    queryFn: () => detectionsAPI.getRecentDetections(),
  });

  const { data: alertsData } = useQuery({ // This might be replaceable
    queryKey: ['critical-alerts'],
    queryFn: () => alertsAPI.getCriticalAlerts(),
  });

  const { data: investigationsData } = useQuery({ // This might be replaceable
    queryKey: ['recent-investigations'],
    queryFn: () => investigationsAPI.getRecentInvestigations(),
  });

  // Statistiques en fonction du rôle
  const getStats = (): StatCardData[] => {
    if (isLoadingStats || !dashboardStatsData) {
      return [];
    }

    const stats: StatCardData[] = [];
    const {
      total_detections,
      active_alerts,
      pending_investigations,
      total_financial_risk,
      accuracy_rate,
    } = dashboardStatsData;

    // Statistiques communes
    stats.push({
      title: 'Total Détections',
      value: total_detections,
      icon: MagnifyingGlassIcon,
      color: 'bg-gradient-blue',
      trend: { value: 12.5, isPositive: true },
    });

    // Statistiques pour Responsable Régional et Administrateur
    if (hasAuthority('Responsable Régional') || hasAuthority('Administrateur')) {
      stats.push(
        {
          title: 'Alertes Actives',
          value: active_alerts,
          icon: ExclamationTriangleIcon,
          color: 'bg-gradient-red',
          trend: { value: 8.2, isPositive: false },
        },
        {
          title: 'Investigations en Cours',
          value: pending_investigations,
          icon: DocumentTextIcon,
          color: 'bg-gradient-green',
          trend: { value: 15.3, isPositive: true },
        },
        {
          title: 'Risque Financier (FCFA)',
          value: Math.round(total_financial_risk / 1000000), // En millions
          icon: BanknotesIcon,
          color: 'bg-gradient-to-r from-amber-500 to-orange-500',
          trend: { value: 5.7, isPositive: false },
        },
        {
          title: 'Taux de Précision (%)',
          value: Math.round(accuracy_rate * 100),
          icon: ShieldCheckIcon,
          color: 'bg-gradient-to-r from-emerald-500 to-teal-500',
          trend: { value: 2.1, isPositive: true },
        }
      );
    }

    // Statistiques pour Agent Terrain
    if (hasAuthority('Agent Terrain')) {
      const investigations = investigationsData?.data || investigationsData || [];
      const detections = detectionsData?.data || detectionsData || [];

      stats.push(
        {
          title: 'Mes Investigations',
          value: Array.isArray(investigations) ? investigations.filter((inv: any) => inv.assigned_to === user?.id).length : 0,
          icon: UsersIcon,
          color: 'bg-gradient-green',
        },
        {
          title: 'Détections en Attente',
          value: Array.isArray(detections) ? detections.filter((det: any) => det.status === 'pending').length : 0,
          icon: ClockIcon,
          color: 'bg-gradient-gold',
        }
      );
    }

    return stats;
  };

  // Données pour les graphiques
  const detectionTrendData = {
    labels: ['Jan', 'Fév', 'Mar', 'Avr', 'Mai', 'Jun'],
    datasets: [
      {
        label: 'Détections',
        data: [12, 19, 15, 25, 22, 30],
        borderColor: '#0B1E3F',
        backgroundColor: 'rgba(11, 30, 63, 0.1)',
      },
    ],
  };

  const alertsDistributionData = {
    labels: ['Critique', 'Élevé', 'Moyen', 'Faible'],
    datasets: [
      {
        data: [5, 12, 18, 8],
        backgroundColor: ['#E63946', '#F97316', '#F59E0B', '#10B981'],
        borderColor: ['#DC2626', '#EA580C', '#D97706', '#059669'],
        borderWidth: 2,
      },
    ],
  };

  const monthlyStatsData = {
    labels: ['Jan', 'Fév', 'Mar', 'Avr', 'Mai', 'Jun'],
    datasets: [
      {
        label: 'Détections',
        data: [12, 19, 15, 25, 22, 30],
        backgroundColor: ['#0B1E3F', '#1E40AF', '#3B82F6', '#60A5FA', '#93C5FD', '#DBEAFE'],
        borderColor: ['#0B1E3F', '#1E40AF', '#3B82F6', '#60A5FA', '#93C5FD', '#DBEAFE'],
      },
    ],
  };

  return (
    <div className="space-y-8">
      {/* En-tête moderne */}
      <motion.div
        initial={{ opacity: 0, y: -20 }}
        animate={{ opacity: 1, y: 0 }}
        className="bg-gradient-to-r from-night-blue to-slate-800 rounded-2xl p-8 text-white shadow-2xl"
      >
        <div className="flex items-center justify-between">
          <div>
            <h1 className="text-3xl font-bold mb-2">
              Tableau de bord
            </h1>
            <p className="text-slate-300 text-lg">
              Bienvenue, <span className="text-gold font-semibold">{user?.full_name}</span>
            </p>
            <div className="flex items-center mt-2 space-x-4">
              <span className="text-sm text-slate-400">{user?.job_title}</span>
              <span className="text-sm text-slate-400">•</span>
              <span className="text-sm text-slate-400 flex items-center">
                <MapPinIcon className="w-4 h-4 mr-1" />
                {user?.authorized_region}
              </span>
            </div>
          </div>
          <div className="hidden md:block">
            <div className="w-24 h-24 bg-gradient-gold rounded-full flex items-center justify-center shadow-glow-gold">
              <ChartPieIcon className="w-12 h-12 text-night-blue" />
            </div>
          </div>
        </div>
      </motion.div>

      {/* Statistiques modernes */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6">
        {getStats().map((stat, index) => (
          <motion.div
            key={index}
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ delay: index * 0.1 }}
          >
            <StatCard
              title={stat.title}
              value={stat.value.toLocaleString()}
              icon={stat.icon}
              color={stat.color}
              trend={stat.trend}
            />
          </motion.div>
        ))}
      </div>

      {/* Graphiques et analyses */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
        <motion.div
          initial={{ opacity: 0, x: -20 }}
          animate={{ opacity: 1, x: 0 }}
          transition={{ delay: 0.3 }}
        >
          <LineChart
            title="Évolution des Détections"
            data={detectionTrendData}
          />
        </motion.div>

        <motion.div
          initial={{ opacity: 0, x: 20 }}
          animate={{ opacity: 1, x: 0 }}
          transition={{ delay: 0.4 }}
        >
          <DoughnutChart
            title="Distribution des Alertes"
            data={alertsDistributionData}
          />
        </motion.div>
      </div>

      {/* Graphique mensuel */}
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ delay: 0.5 }}
      >
        <BarChart
          title="Statistiques Mensuelles"
          data={monthlyStatsData}
        />
      </motion.div>

      {/* Alertes critiques modernes */}
      {(hasAuthority('Responsable Régional') || hasAuthority('Administrateur')) && (
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.6 }}
        >
          <Card className="overflow-hidden">
            <div className="flex items-center justify-between mb-6">
              <h2 className="text-xl font-semibold text-slate-900 flex items-center">
                <ExclamationTriangleIcon className="w-5 h-5 mr-2 text-red-500" />
                Alertes Critiques
              </h2>
              <span className="px-3 py-1 bg-red-100 text-red-800 rounded-full text-sm font-medium">
                Urgent
              </span>
            </div>
            {(() => {
              const alerts = alertsData?.data || alertsData || [];
              return Array.isArray(alerts) && alerts.length > 0 ? (
                <div className="space-y-4">
                  {alerts.map((alert: any, index: number) => (
                    <motion.div
                      key={alert.id}
                      initial={{ opacity: 0, x: -20 }}
                      animate={{ opacity: 1, x: 0 }}
                      transition={{ delay: 0.1 * index }}
                      className="bg-gradient-to-r from-red-50 to-orange-50 p-4 rounded-xl border-l-4 border-red-500 hover:shadow-md transition-shadow"
                    >
                      <div className="flex items-start justify-between">
                        <div className="flex-1">
                          <h3 className="font-semibold text-red-900">{alert.name || alert.title}</h3>
                          <p className="text-sm text-red-700 mt-1">{alert.message || alert.description}</p>
                          <div className="flex items-center mt-3 text-xs text-red-600">
                            <ClockIcon className="w-3 h-3 mr-1" />
                            {new Date(alert.created_at).toLocaleDateString('fr-FR')}
                          </div>
                        </div>
                        <div className="ml-4">
                          <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-red-100 text-red-800">
                            Critique
                          </span>
                        </div>
                      </div>
                    </motion.div>
                  ))}
                </div>
              ) : (
                <div className="text-center py-8">
                  <ShieldCheckIcon className="w-12 h-12 text-green-500 mx-auto mb-3" />
                  <p className="text-slate-500">Aucune alerte critique</p>
                  <p className="text-sm text-slate-400">Tout semble normal</p>
                </div>
              );
            })()}
          </Card>
        </motion.div>
      )}

      {/* Images récentes modernes */}
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ delay: 0.7 }}
      >
        <Card className="overflow-hidden">
          <div className="flex items-center justify-between mb-6">
            <h2 className="text-xl font-semibold text-slate-900 flex items-center">
              <CameraIcon className="w-5 h-5 mr-2 text-blue-500" />
              Images Récentes
            </h2>
            <span className="text-sm text-slate-500">Dernières analyses</span>
          </div>
          {(() => {
            const images = imagesData?.data || imagesData || [];
            return Array.isArray(images) && images.length > 0 ? (
              <div className="grid grid-cols-2 sm:grid-cols-3 md:grid-cols-4 lg:grid-cols-6 gap-4">
                {images.map((image: any, index: number) => (
                  <motion.div
                    key={image.id}
                    initial={{ opacity: 0, scale: 0.8 }}
                    animate={{ opacity: 1, scale: 1 }}
                    transition={{ delay: 0.1 * index }}
                    className="group relative aspect-square bg-slate-200 rounded-xl overflow-hidden hover:shadow-lg transition-all duration-300"
                  >
                    {image.url ? (
                      <img
                        src={image.url}
                        alt={image.name || image.filename}
                        className="w-full h-full object-cover group-hover:scale-110 transition-transform duration-300"
                      />
                    ) : (
                      <div className="w-full h-full flex items-center justify-center text-slate-500 bg-gradient-to-br from-slate-100 to-slate-200">
                        <div className="text-center">
                          <CameraIcon className="w-8 h-8 mx-auto mb-2" />
                          <span className="text-xs">Image satellite</span>
                        </div>
                      </div>
                    )}
                    <div className="absolute inset-0 bg-gradient-to-t from-black/70 via-transparent to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300" />
                    <div className="absolute bottom-0 left-0 right-0 p-3 text-white transform translate-y-full group-hover:translate-y-0 transition-transform duration-300">
                      <p className="text-xs font-medium truncate">{image.name || image.filename || 'Image sans nom'}</p>
                      <p className="text-xs text-slate-300 flex items-center mt-1">
                        <MapPinIcon className="w-3 h-3 mr-1" />
                        {image.satellite_source || 'Source inconnue'}
                      </p>
                    </div>
                  </motion.div>
                ))}
              </div>
            ) : (
              <div className="text-center py-12">
                <CameraIcon className="w-16 h-16 text-slate-300 mx-auto mb-4" />
                <p className="text-slate-500 text-lg">Aucune image récente</p>
                <p className="text-sm text-slate-400">Les nouvelles analyses apparaîtront ici</p>
              </div>
            );
          })()}
        </Card>
      </motion.div>
    </div>
  );
};