# Generated by Django 5.2.1 on 2025-06-02 01:54

from django.conf import settings
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('region', '0003_remove_regionmodel_geographic_zone'),
        ('report', '0002_dashboardstatistic_reportmodel'),
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
    ]

    operations = [
        migrations.AddField(
            model_name='reportmodel',
            name='file_format',
            field=models.CharField(choices=[('CSV', 'CSV'), ('PDF', 'PDF')], default='CSV', help_text='File format of the generated report', max_length=10),
        ),
        migrations.AddField(
            model_name='reportmodel',
            name='processing_error_message',
            field=models.TextField(blank=True, help_text='Details if an error occurred during report generation', null=True),
        ),
        migrations.AlterField(
            model_name='reportmodel',
            name='status',
            field=models.CharField(choices=[('PENDING', 'Pending'), ('PROCESSING', 'Processing'), ('COMPLETED', 'Completed'), ('ERROR', 'Error')], default='PENDING', help_text='Generation status of the report', max_length=20),
        ),
        migrations.AddIndex(
            model_name='reportmodel',
            index=models.Index(fields=['status', 'report_type'], name='reports_status_4f95b8_idx'),
        ),
    ]
