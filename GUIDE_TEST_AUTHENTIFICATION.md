# 🧪 Guide de Test - Authentification Multi-Rôles

## 📋 Objectifs des Tests

Vérifier que le système d'authentification fonctionne correctement avec les différents rôles utilisateur et que l'interface s'adapte selon les permissions.

## 🚀 Préparation

### 1. <PERSON><PERSON><PERSON><PERSON> les serveurs

```bash
# Terminal 1 - Backend Django
cd backend
python manage.py runserver

# Terminal 2 - Frontend React
cd frontend  
npm run dev
```

### 2. Vérifier la base de données

```bash
cd backend
python manage.py migrate
```

## 👥 Comptes de Test Disponibles

| Rôle | Email | Mot de passe | Nom |
|------|-------|--------------|-----|
| **Administrateur** | <EMAIL> | admin123 | Admin System |
| **Responsable Régional** | <EMAIL> | password123 | <PERSON> |
| **Agent Analyste** | <EMAIL> | password123 | Ana<PERSON>ste <PERSON>pert |
| **Agent Technique** | <EMAIL> | password123 | Technicien Support |
| **Agent Terrain** | <EMAIL> | password123 | <PERSON> |

## 🧪 Tests à Effectuer

### Test 1: Connexion Administrateur 👑

**Compte:** <EMAIL> / admin123

**Vérifications:**
- [ ] Connexion réussie
- [ ] Dashboard complet avec toutes les statistiques
- [ ] Navigation complète (toutes les pages visibles)
- [ ] Actions rapides: Analyse, Investigations, Statistiques, Risques Financiers
- [ ] Profil affiche "Administrateur" comme rôle principal
- [ ] Notifications fonctionnelles

**Interface attendue:**
- Toutes les cartes de statistiques
- Section "Actions Rapides" avec 4 boutons
- Menu navigation avec toutes les pages
- Indicateur de rôle "Administrateur" dans la sidebar

---

### Test 2: Connexion Responsable Régional 👔

**Compte:** <EMAIL> / password123

**Vérifications:**
- [ ] Connexion réussie
- [ ] Dashboard avec statistiques étendues
- [ ] Navigation: pas de "Régions" (réservé admin)
- [ ] Actions rapides: Investigations, Statistiques, Risques Financiers
- [ ] Profil affiche "Responsable Régional"
- [ ] Section alertes critiques visible

**Interface attendue:**
- Cartes: Détections, Alertes, Investigations, Précision IA, Impact Financier
- Actions rapides: 3 boutons (pas d'analyse spectrale)
- Menu sans "Régions"
- Alertes critiques affichées

---

### Test 3: Connexion Agent Analyste 🔍

**Compte:** <EMAIL> / password123

**Vérifications:**
- [ ] Connexion réussie
- [ ] Dashboard avec statistiques système
- [ ] Navigation: Spectral visible, pas de Régions
- [ ] Actions rapides: Analyse, Statistiques
- [ ] Profil affiche "Agent Analyste"
- [ ] Pas d'accès aux risques financiers

**Interface attendue:**
- Cartes: Détections, Alertes, Investigations, Précision IA
- Actions rapides: 2 boutons (Analyse + Stats)
- Menu avec "Analyse Spectrale"
- Pas de section risques financiers

---

### Test 4: Connexion Agent Technique 🛠️

**Compte:** <EMAIL> / password123

**Vérifications:**
- [ ] Connexion réussie
- [ ] Dashboard avec statistiques techniques
- [ ] Navigation: pas d'Analyse Spectrale, pas de Régions
- [ ] Actions rapides: Statistiques uniquement
- [ ] Profil affiche "Agent Technique"

**Interface attendue:**
- Cartes: Détections, Alertes, Investigations, Précision IA
- Actions rapides: 1 bouton (Stats techniques)
- Menu sans "Analyse Spectrale" ni "Régions"

---

### Test 5: Connexion Agent Terrain 🚶

**Compte:** <EMAIL> / password123

**Vérifications:**
- [ ] Connexion réussie
- [ ] Dashboard simplifié (statistiques limitées)
- [ ] Navigation: pas de Stats, pas d'Analyse, pas de Régions
- [ ] Pas d'actions rapides ou très limitées
- [ ] Profil affiche "Agent Terrain"
- [ ] Focus sur investigations assignées

**Interface attendue:**
- Cartes: Détections, Alertes, Mes Investigations, Détections en Attente
- Pas d'actions rapides ou très limitées
- Menu réduit (pas de Stats/Spectral/Régions)
- Interface orientée terrain

## 🔍 Tests Spécifiques par Fonctionnalité

### Test A: Page Profil

**Pour chaque rôle:**
- [ ] Modification des informations personnelles
- [ ] Changement de mot de passe
- [ ] Affichage correct des autorités
- [ ] Indicateur de rôle principal

### Test B: Navigation Adaptative

**Vérifier que ces pages sont filtrées:**
- `/stats` - Visible pour: Admin, Responsable, Analyste, Technique
- `/spectral` - Visible pour: Admin, Responsable, Analyste
- `/regions` - Visible pour: Admin uniquement

### Test C: Actions Rapides

**Vérifier la présence selon le rôle:**
- Lancer Analyse: Admin, Responsable, Analyste
- Investigations: Admin, Responsable
- Statistiques: Admin, Responsable, Analyste, Technique
- Risques Financiers: Admin, Responsable

### Test D: Notifications

**Pour tous les rôles:**
- [ ] Badge de notification visible
- [ ] Dropdown fonctionnel
- [ ] Marquage lu/non lu
- [ ] Lien vers page alertes

## 🐛 Problèmes Potentiels à Vérifier

### Erreurs Communes

1. **Token expiré**
   - Symptôme: Redirection vers login après quelques minutes
   - Solution: Vérifier le refresh automatique

2. **Permissions incorrectes**
   - Symptôme: Pages accessibles qui ne devraient pas l'être
   - Solution: Vérifier les hooks de permissions

3. **Interface non adaptée**
   - Symptôme: Même dashboard pour tous les rôles
   - Solution: Vérifier les conditions dans DashboardPage

4. **Navigation non filtrée**
   - Symptôme: Toutes les pages visibles pour tous
   - Solution: Vérifier getFilteredNavigation()

## ✅ Critères de Réussite

### Fonctionnalités de Base
- [ ] Connexion/déconnexion pour tous les rôles
- [ ] Redirection automatique selon les permissions
- [ ] Profil utilisateur fonctionnel
- [ ] Navigation adaptée au rôle

### Interface Utilisateur
- [ ] Dashboard adaptatif selon les permissions
- [ ] Actions rapides contextuelles
- [ ] Notifications en temps réel
- [ ] Design cohérent et responsive

### Sécurité
- [ ] Accès restreint selon les rôles
- [ ] Tokens JWT fonctionnels
- [ ] Refresh automatique des tokens
- [ ] Déconnexion sécurisée

## 📊 Rapport de Test

Après chaque test, noter:

| Rôle | Connexion | Dashboard | Navigation | Profil | Actions | Notes |
|------|-----------|-----------|------------|--------|---------|-------|
| Admin | ✅/❌ | ✅/❌ | ✅/❌ | ✅/❌ | ✅/❌ | |
| Responsable | ✅/❌ | ✅/❌ | ✅/❌ | ✅/❌ | ✅/❌ | |
| Analyste | ✅/❌ | ✅/❌ | ✅/❌ | ✅/❌ | ✅/❌ | |
| Technique | ✅/❌ | ✅/❌ | ✅/❌ | ✅/❌ | ✅/❌ | |
| Terrain | ✅/❌ | ✅/❌ | ✅/❌ | ✅/❌ | ✅/❌ | |

## 🚀 Prochaines Étapes

Une fois tous les tests validés:
1. Implémenter le module Détections
2. Ajouter le système d'assignation d'alertes
3. Développer la liste des investigations
4. Intégrer Google Earth Engine réel
