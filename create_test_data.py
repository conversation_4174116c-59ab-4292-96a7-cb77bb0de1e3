#!/usr/bin/env python3
"""
Script pour créer des données de test pour l'analyse spectrale
"""
import os
import sys
import django
from datetime import datetime, timedelta
import random

# Configuration Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'config.settings')
django.setup()

from django.utils import timezone
from image.models.image_model import ImageModel
from region.models.region_model import RegionModel


def create_test_region():
    """Créer la région de test Bondoukou"""
    region, created = RegionModel.objects.get_or_create(
        name='BONDOUKOU',
        defaults={
            'code': 'BDK',
            'area_km2': 12000,
            'center_lat': 8.0,
            'center_lon': -3.0
        }
    )
    if created:
        print(f"✅ Région créée : {region.name}")
    else:
        print(f"ℹ️ Région existante : {region.name}")
    return region


def generate_spectral_data():
    """Générer des données spectrales réalistes"""
    # Valeurs réalistes pour la région de Bondoukou
    ndvi_mean = random.uniform(0.3, 0.7)  # Végétation modérée à dense
    ndwi_mean = random.uniform(-0.1, 0.2)  # Humidité variable
    ndti_mean = random.uniform(0.05, 0.15)  # Sol légèrement perturbé

    return {
        'ndvi_data': {
            'mean': round(ndvi_mean, 3),
            'stddev': round(random.uniform(0.05, 0.15), 3),
            'computed_at': timezone.now().isoformat()
        },
        'ndwi_data': {
            'mean': round(ndwi_mean, 3),
            'stddev': round(random.uniform(0.03, 0.10), 3),
            'computed_at': timezone.now().isoformat()
        },
        'ndti_data': {
            'mean': round(ndti_mean, 3),
            'stddev': round(random.uniform(0.02, 0.08), 3),
            'computed_at': timezone.now().isoformat()
        },
        'ndvi_mean': ndvi_mean,
        'ndwi_mean': ndwi_mean,
        'ndti_mean': ndti_mean
    }


def create_test_images(region, count=10):
    """Créer des images de test avec données spectrales"""
    created_count = 0

    for i in range(count):
        # Date de capture (derniers 30 jours)
        days_ago = random.randint(1, 30)
        capture_date = (timezone.now() - timedelta(days=days_ago)).date()

        # Asset ID unique
        gee_asset_id = f"COPERNICUS/S2_SR/20241201T{100000 + i*10000}_20241201T{100000 + i*10000 + 1000}_T30NXP"

        # Vérifier si l'image existe déjà
        if ImageModel.objects.filter(gee_asset_id=gee_asset_id).exists():
            print(f"⏭️ Image {i+1} existe déjà")
            continue

        # Générer données spectrales
        spectral_data = generate_spectral_data()

        # Créer l'image
        image = ImageModel.objects.create(
            name=f"Test_Sentinel2_{capture_date.strftime('%Y%m%d')}_{i+1:02d}",
            region=region,
            capture_date=capture_date,
            satellite_source='SENTINEL2',
            cloud_coverage=random.uniform(5, 25),
            resolution=10,
            gee_asset_id=gee_asset_id,
            gee_collection='COPERNICUS/S2_SR',
            center_lat=8.0 + random.uniform(-0.5, 0.5),
            center_lon=-3.0 + random.uniform(-0.5, 0.5),
            processing_status='COMPLETED',
            # Données spectrales
            ndvi_data=spectral_data['ndvi_data'],
            ndwi_data=spectral_data['ndwi_data'],
            ndti_data=spectral_data['ndti_data'],
            ndvi_mean=spectral_data['ndvi_mean'],
            ndwi_mean=spectral_data['ndwi_mean'],
            ndti_mean=spectral_data['ndti_mean'],
            processed_at=timezone.now()
        )

        created_count += 1
        print(f"✅ Image créée : {image.name} (ID: {image.id})")

    print(f"\n🎉 {created_count} nouvelles images créées sur {count} demandées")


def create_anomaly_image(region):
    """Créer une image avec des anomalies pour tester les alertes"""
    gee_asset_id = "COPERNICUS/S2_SR/ANOMALY_TEST_20241201T120000"

    if ImageModel.objects.filter(gee_asset_id=gee_asset_id).exists():
        print("⏭️ Image d'anomalie existe déjà")
        return

    # Données avec anomalies (NDTI élevé = perturbation du sol)
    anomaly_data = {
        'ndvi_data': {
            'mean': 0.15,  # Végétation dégradée
            'stddev': 0.08,
            'computed_at': timezone.now().isoformat()
        },
        'ndwi_data': {
            'mean': 0.25,  # Présence d'eau (bassins de décantation)
            'stddev': 0.12,
            'computed_at': timezone.now().isoformat()
        },
        'ndti_data': {
            'mean': 0.18,  # Sol fortement perturbé (ALERTE!)
            'stddev': 0.05,
            'computed_at': timezone.now().isoformat()
        },
        'ndvi_mean': 0.15,
        'ndwi_mean': 0.25,
        'ndti_mean': 0.18
    }

    image = ImageModel.objects.create(
        name="ANOMALIE_Orpaillage_Detecte_20241201",
        region=region,
        capture_date=timezone.now().date(),
        satellite_source='SENTINEL2',
        cloud_coverage=8.5,
        resolution=10,
        gee_asset_id=gee_asset_id,
        gee_collection='COPERNICUS/S2_SR',
        center_lat=8.1,
        center_lon=-2.9,
        processing_status='COMPLETED',
        ndvi_data=anomaly_data['ndvi_data'],
        ndwi_data=anomaly_data['ndwi_data'],
        ndti_data=anomaly_data['ndti_data'],
        ndvi_mean=anomaly_data['ndvi_mean'],
        ndwi_mean=anomaly_data['ndwi_mean'],
        ndti_mean=anomaly_data['ndti_mean'],
        processed_at=timezone.now()
    )

    print(f"🚨 Image d'anomalie créée : {image.name} (ID: {image.id})")
    print(f"   NDVI: {image.ndvi_mean} (dégradé)")
    print(f"   NDWI: {image.ndwi_mean} (eau présente)")
    print(f"   NDTI: {image.ndti_mean} (sol perturbé - ALERTE!)")


def main():
    print("🚀 Création de données de test pour l'analyse spectrale")
    print("=" * 60)

    # Créer la région
    region = create_test_region()

    # Créer des images normales
    print(f"\n📸 Création d'images de test...")
    create_test_images(region, count=15)

    # Créer une image avec anomalie
    print(f"\n🚨 Création d'image avec anomalie...")
    create_anomaly_image(region)

    # Statistiques finales
    total_images = ImageModel.objects.filter(region=region).count()
    completed_images = ImageModel.objects.filter(
        region=region,
        processing_status='COMPLETED'
    ).count()

    print(f"\n📊 Statistiques finales :")
    print(f"   Total images : {total_images}")
    print(f"   Images traitées : {completed_images}")
    print(f"   Région : {region.name}")

    print(f"\n✅ Données de test créées avec succès !")
    print(f"🌐 Vous pouvez maintenant tester l'interface spectrale")


if __name__ == "__main__":
    main()
