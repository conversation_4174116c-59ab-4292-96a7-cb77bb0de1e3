#!/usr/bin/env python3
"""
Script de vérification rapide du système GoldSentinel
Vérifie la configuration, les dépendances et l'état des services
"""

import os
import sys
import subprocess
import json
from pathlib import Path

def print_header(title):
    print(f"\n{'='*60}")
    print(f" {title}")
    print('='*60)

def print_status(message, status="INFO"):
    icons = {"INFO": "ℹ️", "SUCCESS": "✅", "WARNING": "⚠️", "ERROR": "❌"}
    print(f"{icons.get(status, 'ℹ️')} {message}")

def check_file_exists(filepath, description):
    if os.path.exists(filepath):
        print_status(f"{description}: Présent", "SUCCESS")
        return True
    else:
        print_status(f"{description}: Manquant", "ERROR")
        return False

def check_directory_structure():
    print_header("STRUCTURE DU PROJET")
    
    required_dirs = [
        ("backend/", "Répertoire backend Django"),
        ("frontend/", "Répertoire frontend React"),
        ("backend/manage.py", "Script Django"),
        ("frontend/package.json", "Configuration npm"),
        ("backend/requirements.txt", "Dépendances Python"),
    ]
    
    all_good = True
    for path, desc in required_dirs:
        if not check_file_exists(path, desc):
            all_good = False
    
    return all_good

def check_python_environment():
    print_header("ENVIRONNEMENT PYTHON")
    
    # Vérifier Python
    try:
        python_version = subprocess.check_output([sys.executable, "--version"], text=True).strip()
        print_status(f"Python: {python_version}", "SUCCESS")
    except Exception as e:
        print_status(f"Python: Erreur - {e}", "ERROR")
        return False
    
    # Vérifier pip
    try:
        pip_version = subprocess.check_output([sys.executable, "-m", "pip", "--version"], text=True).strip()
        print_status(f"Pip: {pip_version.split()[1]}", "SUCCESS")
    except Exception as e:
        print_status(f"Pip: Erreur - {e}", "ERROR")
        return False
    
    # Vérifier l'environnement virtuel
    if hasattr(sys, 'real_prefix') or (hasattr(sys, 'base_prefix') and sys.base_prefix != sys.prefix):
        print_status("Environnement virtuel: Activé", "SUCCESS")
    else:
        print_status("Environnement virtuel: Non activé", "WARNING")
    
    return True

def check_node_environment():
    print_header("ENVIRONNEMENT NODE.JS")
    
    # Vérifier Node.js
    try:
        node_version = subprocess.check_output(["node", "--version"], text=True).strip()
        print_status(f"Node.js: {node_version}", "SUCCESS")
    except Exception as e:
        print_status(f"Node.js: Non installé - {e}", "ERROR")
        return False
    
    # Vérifier npm
    try:
        npm_version = subprocess.check_output(["npm", "--version"], text=True).strip()
        print_status(f"npm: {npm_version}", "SUCCESS")
    except Exception as e:
        print_status(f"npm: Non installé - {e}", "ERROR")
        return False
    
    # Vérifier node_modules
    if os.path.exists("frontend/node_modules"):
        print_status("Dépendances npm: Installées", "SUCCESS")
    else:
        print_status("Dépendances npm: Non installées", "WARNING")
        print_status("Exécutez: cd frontend && npm install", "INFO")
    
    return True

def check_django_setup():
    print_header("CONFIGURATION DJANGO")
    
    if not os.path.exists("backend/manage.py"):
        print_status("manage.py non trouvé", "ERROR")
        return False
    
    # Changer vers le répertoire backend
    original_dir = os.getcwd()
    try:
        os.chdir("backend")
        
        # Vérifier les migrations
        try:
            result = subprocess.run([sys.executable, "manage.py", "showmigrations", "--plan"], 
                                  capture_output=True, text=True, timeout=10)
            if result.returncode == 0:
                print_status("Migrations: Configurées", "SUCCESS")
            else:
                print_status("Migrations: Problème détecté", "WARNING")
        except Exception as e:
            print_status(f"Migrations: Erreur - {e}", "ERROR")
        
        # Vérifier les utilisateurs de test
        try:
            result = subprocess.run([
                sys.executable, "manage.py", "shell", "-c",
                "from account.models import UserModel; print(f'Utilisateurs: {UserModel.objects.count()}')"
            ], capture_output=True, text=True, timeout=10)
            
            if result.returncode == 0:
                print_status(f"Base de données: {result.stdout.strip()}", "SUCCESS")
            else:
                print_status("Base de données: Non accessible", "WARNING")
        except Exception as e:
            print_status(f"Base de données: Erreur - {e}", "ERROR")
            
    finally:
        os.chdir(original_dir)
    
    return True

def check_test_files():
    print_header("FICHIERS DE TEST")
    
    test_files = [
        ("test_authentication.py", "Script de test API"),
        ("start_test_servers.sh", "Script de démarrage"),
        ("GUIDE_TEST_AUTHENTIFICATION.md", "Guide de test"),
        ("README_TESTS_AUTH.md", "Documentation tests"),
    ]
    
    for filepath, desc in test_files:
        check_file_exists(filepath, desc)

def check_frontend_config():
    print_header("CONFIGURATION FRONTEND")
    
    # Vérifier package.json
    if os.path.exists("frontend/package.json"):
        try:
            with open("frontend/package.json", 'r') as f:
                package_data = json.load(f)
            
            print_status(f"Nom du projet: {package_data.get('name', 'Non défini')}", "INFO")
            print_status(f"Version: {package_data.get('version', 'Non définie')}", "INFO")
            
            # Vérifier les dépendances importantes
            deps = package_data.get('dependencies', {})
            important_deps = ['react', 'typescript', '@tanstack/react-query', 'framer-motion']
            
            for dep in important_deps:
                if dep in deps:
                    print_status(f"{dep}: {deps[dep]}", "SUCCESS")
                else:
                    print_status(f"{dep}: Manquant", "WARNING")
                    
        except Exception as e:
            print_status(f"Erreur lecture package.json: {e}", "ERROR")
    
    # Vérifier les fichiers de configuration
    config_files = [
        ("frontend/vite.config.ts", "Configuration Vite"),
        ("frontend/tailwind.config.js", "Configuration Tailwind"),
        ("frontend/tsconfig.json", "Configuration TypeScript"),
    ]
    
    for filepath, desc in config_files:
        check_file_exists(filepath, desc)

def provide_recommendations():
    print_header("RECOMMANDATIONS")
    
    print_status("Pour démarrer les tests d'authentification:", "INFO")
    print("   1. ./start_test_servers.sh")
    print("   2. Ou manuellement:")
    print("      Terminal 1: cd backend && python manage.py runserver")
    print("      Terminal 2: cd frontend && npm run dev")
    print("   3. Ouvrir http://localhost:5173")
    print("   4. Tester avec les comptes dans README_TESTS_AUTH.md")
    
    print_status("Pour tester l'API:", "INFO")
    print("   python test_authentication.py")
    
    print_status("Pour débugger:", "INFO")
    print("   - Utiliser le panneau de debug (bouton violet)")
    print("   - Vérifier la console browser (F12)")
    print("   - Consulter les logs Django")

def main():
    print_header("VÉRIFICATION SYSTÈME GOLDSENTINEL")
    print("🔍 Vérification de l'état du système...")
    
    checks = [
        check_directory_structure(),
        check_python_environment(),
        check_node_environment(),
        check_django_setup(),
        check_frontend_config(),
    ]
    
    check_test_files()
    
    print_header("RÉSUMÉ")
    
    if all(checks):
        print_status("Système prêt pour les tests d'authentification!", "SUCCESS")
    else:
        print_status("Certains problèmes détectés - voir les détails ci-dessus", "WARNING")
    
    provide_recommendations()

if __name__ == "__main__":
    main()
