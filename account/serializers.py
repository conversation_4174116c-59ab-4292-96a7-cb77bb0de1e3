from rest_framework import serializers
from .models import UserModel, UserAuthorityModel

class UserProfileSerializer(serializers.ModelSerializer):
    full_name = serializers.CharField(source='get_full_name', read_only=True)
    authorities = serializers.SerializerMethodField()
    primary_authority = serializers.SerializerMethodField()

    class Meta:
        model = UserModel
        fields = [
            'id', 'email', 'first_name', 'last_name', 'full_name',
            'job_title', 'institution', 'authorized_region',
            'authorities', 'primary_authority', 'created_at', 'last_login'
        ]
        read_only_fields = ['id', 'email', 'created_at', 'last_login']

    def get_authorities(self, obj):
        """Récupère toutes les autorités de l'utilisateur"""
        user_authorities = UserAuthorityModel.objects.filter(user=obj)
        return [
            {
                'name': ua.authority.name,
                'status': ua.is_primary
            }
            for ua in user_authorities
        ]

    def get_primary_authority(self, obj):
        """Récupère l'autorité principale de l'utilisateur"""
        primary_authority = UserAuthorityModel.objects.filter(
            user=obj, is_primary=True
        ).first()
        return primary_authority.authority.name if primary_authority else None