import datetime
from django.core.management.base import BaseCommand, CommandError
from django.contrib.auth.hashers import make_password
from django.db import IntegrityError, transaction

from account.models import UserModel, AuthorityModel, UserAuthorityModel
from image.models.image_model import ImageModel
from detection.models.detection_model import DetectionModel
from detection.models.investigation_model import InvestigationModel
from region.models.region_model import RegionModel # Adjusted import

class Command(BaseCommand):
    help = 'Creates default users, authorities, and optionally demo data. Also creates/updates a default region for demo.'

    AUTHORITY_CHOICES_TUPLE = [
        ('Administrateur', 'Administrateur'),
        ('Responsable Régional', 'Responsable Régional'),
        ('Agent Terrain', 'Agent Terrain'),
        ('Agent Technique', 'Agent Technique'),
        ('Agent Analyste', 'Agent Analyste'),
    ]

    def add_arguments(self, parser):
        parser.add_argument(
            '--force',
            action='store_true',
            help='Delete existing users (except superusers), their authorities, and related demo data before creating new ones.'
        )
        parser.add_argument(
            '--demo',
            action='store_true',
            help='Create additional demo data (image, detection, investigation, and ensure Bondoukou region exists).'
        )

    @transaction.atomic
    def handle(self, *args, **options):
        force = options['force']
        create_demo = options['demo']

        if force:
            self.stdout.write(self.style.WARNING('Force option selected. Deleting existing data...'))
            # Order of deletion matters due to foreign key constraints
            InvestigationModel.objects.all().delete()
            self.stdout.write(self.style.SUCCESS('Successfully deleted all InvestigationModel objects.'))
            DetectionModel.objects.all().delete()
            self.stdout.write(self.style.SUCCESS('Successfully deleted all DetectionModel objects.'))
            ImageModel.objects.filter(name__icontains='Demo').delete() # Delete only demo images
            self.stdout.write(self.style.SUCCESS('Successfully deleted demo ImageModel objects.'))

            UserAuthorityModel.objects.all().delete()
            self.stdout.write(self.style.SUCCESS('Successfully deleted all UserAuthorityModel objects.'))

            # Keep superusers by default, delete others if force is used
            UserModel.objects.filter(is_superuser=False).delete()
            self.stdout.write(self.style.SUCCESS('Successfully deleted all non-superuser UserModel objects.'))
            # Optionally, delete specific authorities if they were managed by this script,
            # but get_or_create handles existing ones well. For a full reset of authorities:
            # AuthorityModel.objects.filter(name__in=[choice[0] for choice in self.AUTHORITY_CHOICES_TUPLE]).delete()
            # self.stdout.write(self.style.SUCCESS('Successfully deleted managed AuthorityModel objects.'))

        self.stdout.write(self.style.HTTP_INFO('Creating/Verifying Authorities...'))
        roles = {}
        for role_value, role_display_name in self.AUTHORITY_CHOICES_TUPLE:
            try:
                authority, created = AuthorityModel.objects.get_or_create(name=role_value)
                roles[role_value] = authority
                if created:
                    self.stdout.write(self.style.SUCCESS(f'Authority "{role_display_name}" created.'))
                else:
                    self.stdout.write(self.style.NOTICE(f'Authority "{role_display_name}" already exists.'))
            except Exception as e:
                self.stderr.write(self.style.ERROR(f'Error creating authority "{role_display_name}": {e}'))
                return # Stop if authorities can't be set up

        self.stdout.write(self.style.HTTP_INFO('Creating Default Users...'))
        users_data = [
            {
                'email': '<EMAIL>', 'password': 'adminpassword', 'first_name': 'Admin', 'last_name': 'System',
                'is_staff': True, 'is_superuser': True, 'authority_name': 'Administrateur', 'is_primary': True
            },
            {
                'email': '<EMAIL>', 'password': 'respopassword', 'first_name': 'Jean', 'last_name': 'Dupont',
                'authority_name': 'Responsable Régional', 'is_primary': True
            },
            {
                'email': '<EMAIL>', 'password': 'agentpassword', 'first_name': 'Marie', 'last_name': 'Martin',
                'authority_name': 'Agent Terrain', 'is_primary': True
            },
            {
                'email': '<EMAIL>', 'password': 'agentpassword', 'first_name': 'Pierre', 'last_name': 'Durand',
                'authority_name': 'Agent Terrain', 'is_primary': True
            },
            {
                'email': '<EMAIL>', 'password': 'analystpassword', 'first_name': 'Analyste', 'last_name': 'Expert',
                'authority_name': 'Agent Analyste', 'is_primary': True
            },
            {
                'email': '<EMAIL>', 'password': 'techpassword', 'first_name': 'Technicien', 'last_name': 'Support',
                'authority_name': 'Agent Technique', 'is_primary': True
            },
        ]

        agent_terrain_user_for_demo = None

        for user_data in users_data:
            try:
                user, created = UserModel.objects.get_or_create(
                    email=user_data['email'],
                    defaults={
                        'first_name': user_data['first_name'],
                        'last_name': user_data['last_name'],
                        'is_staff': user_data.get('is_staff', False),
                        'is_superuser': user_data.get('is_superuser', False),
                        'password': make_password(user_data['password']),
                    }
                )
                if created:
                     # For existing users (if --force not used), update password if needed, or other fields
                    self.stdout.write(self.style.SUCCESS(f'User {user.email} created.'))
                elif user_data.get('is_superuser') and not user.is_superuser :
                    # if <EMAIL> exists but is not superuser, make it superuser
                    user.is_superuser = True
                    user.is_staff = True # Superusers must be staff
                    user.set_password(user_data['password']) # reset password
                    user.save()
                    self.stdout.write(self.style.WARNING(f'User {user.email} already existed and was updated to be a superuser.'))
                else:
                    self.stdout.write(self.style.NOTICE(f'User {user.email} already exists. Skipping creation.'))

                # Link authority
                authority = roles.get(user_data['authority_name'])
                if authority:
                    ua, ua_created = UserAuthorityModel.objects.get_or_create(
                        user=user, authority=authority,
                        defaults={'is_primary': user_data.get('is_primary', False)}
                    )
                    if ua_created:
                        self.stdout.write(self.style.SUCCESS(f'Linked {user.email} to {authority.name} authority.'))
                    else:
                         # If link exists, ensure is_primary is correctly set
                        if ua.is_primary != user_data.get('is_primary', False):
                            ua.is_primary = user_data.get('is_primary', False)
                            ua.save()
                            self.stdout.write(self.style.NOTICE(f'Updated primary status for {user.email} with {authority.name} authority.'))
                        else:
                            self.stdout.write(self.style.NOTICE(f'{user.email} already linked to {authority.name} authority.'))


                if user_data['authority_name'] == 'Agent Terrain' and not agent_terrain_user_for_demo:
                    agent_terrain_user_for_demo = user

            except IntegrityError as e:
                self.stderr.write(self.style.ERROR(f'Error creating user {user_data["email"]}: {e}. This might happen if email exists with different casing and DB collation is case-sensitive.'))
            except Exception as e:
                self.stderr.write(self.style.ERROR(f'An unexpected error occurred for user {user_data["email"]}: {e}'))


        if create_demo:
            self.stdout.write(self.style.HTTP_INFO('Creating Enhanced Demo Data (10 items each)...'))
            try:
                import random

                # 1. Create Multiple Regions
                regions_data = [
                    {'name': 'BONDOUKOU', 'code': 'BKO', 'area_km2': 12500, 'center_lat': 8.0402, 'center_lon': -2.8000},
                    {'name': 'BOUNA', 'code': 'BNA', 'area_km2': 8500, 'center_lat': 9.2697, 'center_lon': -3.0974},
                    {'name': 'TANDA', 'code': 'TDA', 'area_km2': 7200, 'center_lat': 7.8097, 'center_lon': -3.4974},
                    {'name': 'NASSIAN', 'code': 'NSN', 'area_km2': 6800, 'center_lat': 8.4500, 'center_lon': -3.2000},
                ]

                regions = []
                for region_data in regions_data:
                    region, region_created = RegionModel.objects.get_or_create(
                        name=region_data['name'],
                        defaults=region_data
                    )
                    regions.append(region)
                    if region_created:
                        self.stdout.write(self.style.SUCCESS(f'Region "{region.name}" created.'))
                    else:
                        self.stdout.write(self.style.NOTICE(f'Region "{region.name}" already exists.'))

                # 2. Create 10 Demo Images
                satellites = ['SENTINEL2', 'LANDSAT8', 'LANDSAT9']
                images_created = 0

                for i in range(10):
                    # Varier les dates sur les 6 derniers mois
                    days_ago = random.randint(1, 180)
                    capture_date = datetime.date.today() - datetime.timedelta(days=days_ago)

                    # Choisir une région aléatoire
                    region = random.choice(regions)
                    satellite = random.choice(satellites)

                    demo_image, image_created = ImageModel.objects.get_or_create(
                        name=f"Demo {satellite} Image {region.name} #{i+1:02d}",
                        region=region,
                        defaults={
                            'capture_date': capture_date,
                            'satellite_source': satellite,
                            'cloud_coverage': round(random.uniform(5.0, 45.0), 1),
                            'gee_asset_id': f'projects/demo/assets/{satellite.lower()}_{region.name.lower()}_{i+1:02d}',
                            'center_lat': region.center_lat + random.uniform(-0.1, 0.1),
                            'center_lon': region.center_lon + random.uniform(-0.1, 0.1),
                            'processing_status': random.choice(['COMPLETED', 'COMPLETED', 'COMPLETED', 'PROCESSING'])
                        }
                    )

                    if image_created:
                        images_created += 1
                        self.stdout.write(self.style.SUCCESS(f'Demo Image "{demo_image.name}" created.'))

                        # 3. Create 1-3 Detections per Image
                        num_detections = random.randint(1, 3)
                        detections_created = 0

                        for j in range(num_detections):
                            # Coordonnées dans la région avec variation
                            lat_offset = random.uniform(-0.2, 0.2)
                            lon_offset = random.uniform(-0.2, 0.2)

                            demo_detection, detection_created = DetectionModel.objects.get_or_create(
                                image=demo_image,
                                region=region,
                                latitude=round(region.center_lat + lat_offset, 6),
                                longitude=round(region.center_lon + lon_offset, 6),
                                defaults={
                                    'detection_type': random.choice(['MINING_SITE', 'MINING_SITE', 'DISTURBED_AREA']),
                                    'confidence_score': round(random.uniform(0.65, 0.95), 2),
                                    'area_hectares': round(random.uniform(0.5, 5.0), 1),
                                    'ndvi_anomaly_score': round(random.uniform(0.3, 0.8), 2),
                                    'ndwi_anomaly_score': round(random.uniform(0.2, 0.7), 2),
                                    'ndti_anomaly_score': round(random.uniform(0.4, 0.9), 2),
                                    'validation_status': random.choice(['DETECTED', 'DETECTED', 'VALIDATED', 'FALSE_POSITIVE'])
                                }
                            )

                            if detection_created:
                                detections_created += 1
                                self.stdout.write(self.style.SUCCESS(f'  Detection #{j+1} created for "{demo_image.name}".'))

                                # 4. Create Investigation for some detections (33% chance)
                                if random.choice([True, False, False]):
                                    # Get available agents
                                    agent_terrain_users = UserModel.objects.filter(user_authorities__authority__name='Agent Terrain')
                                    if agent_terrain_users.exists():
                                        investigation_status = random.choice(['PENDING', 'ASSIGNED', 'IN_PROGRESS', 'COMPLETED'])
                                        assigned_agent = random.choice(agent_terrain_users) if investigation_status != 'PENDING' else None

                                        investigation_data = {
                                            'detection': demo_detection,
                                            'status': investigation_status,
                                            'priority': random.choice(['LOW', 'MEDIUM', 'HIGH']),
                                            'target_coordinates': f"{demo_detection.latitude}, {demo_detection.longitude}",
                                            'assignment_notes': f"Investigation automatique pour détection #{j+1} - {demo_image.name}"
                                        }

                                        if assigned_agent:
                                            investigation_data['assigned_to'] = assigned_agent

                                        if investigation_status == 'COMPLETED':
                                            investigation_data['result'] = random.choice(['CONFIRMED', 'FALSE_POSITIVE', 'NEEDS_MONITORING'])
                                            investigation_data['field_notes'] = f"Investigation terminée. Résultat: {investigation_data['result']}"

                                        demo_investigation, inv_created = InvestigationModel.objects.get_or_create(
                                            detection=demo_detection,
                                            defaults=investigation_data
                                        )

                                        if inv_created:
                                            self.stdout.write(self.style.SUCCESS(f'    Investigation created for detection #{j+1}.'))

                # Summary
                total_images = ImageModel.objects.filter(name__startswith='Demo').count()
                total_detections = DetectionModel.objects.filter(image__name__startswith='Demo').count()
                total_investigations = InvestigationModel.objects.filter(detection__image__name__startswith='Demo').count()

                self.stdout.write(self.style.SUCCESS('✅ Enhanced Demo data creation finished!'))
                self.stdout.write(self.style.SUCCESS(f'📊 Total created: {len(regions)} regions, {total_images} images, {total_detections} detections, {total_investigations} investigations'))

            except ImportError as ie:
                 self.stderr.write(self.style.ERROR(f"Failed to import a model for demo data, skipping: {ie}"))
            except Exception as e:
                self.stderr.write(self.style.ERROR(f'Error creating demo data: {e}'))

        self.stdout.write(self.style.SUCCESS('Default user and authority creation process finished.'))
