"""
Service mock pour Google Earth Engine en mode développement
Retourne des données simulées pour tester l'interface sans GEE
"""
import random
from datetime import datetime
from typing import Dict, List, Optional, Tuple
from django.utils import timezone


class MockEarthEngineService:
    """Service simulé pour Google Earth Engine"""

    def __init__(self):
        self.initialized = True  # Toujours initialisé en mode mock
        print("🔧 Mode développement : Service GEE simulé activé")

    def get_recent_images(self, months_back: int = 3) -> List[Dict]:
        """
        Retourne des images simulées pour Bondoukou
        """
        mock_images = []
        for i in range(5):  # 5 images simulées
            mock_images.append({
                'gee_asset_id': f'COPERNICUS/S2_SR/20241201T{100000 + i*10000}',
                'capture_date': datetime(2024, 12, 1 + i).date(),
                'cloud_coverage': random.uniform(5, 25),
                'satellite_source': 'SENTINEL2',
                'resolution': 10,
                'properties': {
                    'system:time_start': int(datetime(2024, 12, 1 + i).timestamp() * 1000),
                    'CLOUDY_PIXEL_PERCENTAGE': random.uniform(5, 25)
                }
            })
        return mock_images

    def calculate_spectral_indices(self, gee_asset_id: str) -> Dict:
        """
        Retourne des indices spectraux simulés
        """
        # Génération de valeurs réalistes avec variation
        base_ndvi = random.uniform(0.3, 0.7)  # Végétation modérée à dense
        base_ndwi = random.uniform(-0.1, 0.2)  # Humidité variable
        base_ndti = random.uniform(0.05, 0.15)  # Sol légèrement perturbé
        
        return {
            'ndvi_data': {
                'mean': round(base_ndvi, 3),
                'stddev': round(random.uniform(0.05, 0.15), 3),
                'computed_at': timezone.now().isoformat()
            },
            'ndwi_data': {
                'mean': round(base_ndwi, 3),
                'stddev': round(random.uniform(0.03, 0.10), 3),
                'computed_at': timezone.now().isoformat()
            },
            'ndti_data': {
                'mean': round(base_ndti, 3),
                'stddev': round(random.uniform(0.02, 0.08), 3),
                'computed_at': timezone.now().isoformat()
            }
        }

    def detect_anomalies(self, current_indices: Dict, reference_indices: Dict) -> Dict:
        """
        Détection d'anomalies simulée
        """
        anomaly_scores = {}
        
        for index_name in ['ndvi', 'ndwi', 'ndti']:
            current_data = current_indices.get(f'{index_name}_data', {})
            reference_data = reference_indices.get(f'{index_name}_data', {})
            
            current_mean = current_data.get('mean', 0)
            reference_mean = reference_data.get('mean', current_mean)
            reference_stddev = reference_data.get('stddev', 0.1)
            
            if reference_stddev > 0:
                anomaly_score = abs(current_mean - reference_mean) / reference_stddev
                anomaly_scores[f'{index_name}_anomaly_score'] = min(anomaly_score, 1.0)
            else:
                anomaly_scores[f'{index_name}_anomaly_score'] = 0.0
        
        return anomaly_scores

    def generate_spectral_maps(self, gee_asset_id: str) -> Dict:
        """
        Génère des URLs de cartes simulées
        """
        # URLs de tuiles simulées (pourraient pointer vers des cartes statiques)
        base_url = "https://earthengine.googleapis.com/v1alpha/projects/earthengine-legacy/maps"
        map_id = f"mock-{abs(hash(gee_asset_id)) % 1000000}"
        
        return {
            'ndvi_map_url': f"{base_url}/{map_id}-ndvi/tiles/{{z}}/{{x}}/{{y}}",
            'ndwi_map_url': f"{base_url}/{map_id}-ndwi/tiles/{{z}}/{{x}}/{{y}}",
            'ndti_map_url': f"{base_url}/{map_id}-ndti/tiles/{{z}}/{{x}}/{{y}}",
            'bounds': {
                'north': 8.5,   # Bondoukou bounds
                'south': 7.5,
                'east': -2.5,
                'west': -3.5
            }
        }

    def get_spectral_patch(self, point_coords: Tuple[float, float],
                          image_asset_id: str,
                          patch_size_pixels: int = 48,
                          scale: int = 10) -> Optional[List]:
        """
        Retourne un patch de données simulées
        """
        # Génération d'un patch simulé
        header = ['longitude', 'latitude', 'time', 'NDVI', 'NDWI', 'NDTI']
        data = [header]
        
        # Génération de points autour des coordonnées
        lon, lat = point_coords
        for i in range(patch_size_pixels):
            for j in range(patch_size_pixels):
                offset_lon = (i - patch_size_pixels/2) * scale / 111320  # Approximation
                offset_lat = (j - patch_size_pixels/2) * scale / 110540
                
                data.append([
                    lon + offset_lon,
                    lat + offset_lat,
                    int(datetime.now().timestamp() * 1000),
                    round(random.uniform(0.2, 0.8), 3),  # NDVI
                    round(random.uniform(-0.2, 0.3), 3),  # NDWI
                    round(random.uniform(0.0, 0.2), 3)   # NDTI
                ])
        
        return data

    def process_image_complete(self, gee_asset_id: str, user_id: int = None):
        """
        Simulation du traitement complet d'une image
        """
        from image.models.image_model import ImageModel
        from region.models.region_model import RegionModel
        
        try:
            # Vérifier si l'image existe déjà
            existing_image = ImageModel.objects.filter(
                gee_asset_id=gee_asset_id,
                processing_status=ImageModel.ProcessingStatus.COMPLETED
            ).first()
            
            if existing_image:
                print(f"🔄 Image {gee_asset_id} déjà traitée (mode mock)")
                return existing_image
            
            # Récupérer ou créer la région Bondoukou
            bondoukou_region, _ = RegionModel.objects.get_or_create(
                name='BONDOUKOU',
                defaults={
                    'code': 'BDK',
                    'area_km2': 12000,
                    'center_lat': 8.0,
                    'center_lon': -3.0
                }
            )
            
            # Générer des données simulées
            indices_data = self.calculate_spectral_indices(gee_asset_id)
            
            # Créer l'enregistrement image avec données simulées
            image_record = ImageModel.objects.create(
                name=f"Mock_Sentinel2_{datetime.now().strftime('%Y%m%d_%H%M%S')}",
                region=bondoukou_region,
                capture_date=datetime.now().date(),
                satellite_source='SENTINEL2',
                cloud_coverage=random.uniform(5, 20),
                resolution=10,
                gee_asset_id=gee_asset_id,
                gee_collection='COPERNICUS/S2_SR',
                center_lat=8.0,
                center_lon=-3.0,
                processing_status=ImageModel.ProcessingStatus.COMPLETED,
                requested_by_id=user_id,
                # Données d'indices simulées
                ndvi_data=indices_data['ndvi_data'],
                ndwi_data=indices_data['ndwi_data'],
                ndti_data=indices_data['ndti_data'],
                ndvi_mean=indices_data['ndvi_data']['mean'],
                ndwi_mean=indices_data['ndwi_data']['mean'],
                ndti_mean=indices_data['ndti_data']['mean'],
                processed_at=timezone.now()
            )
            
            print(f"✅ Image mock créée : ID {image_record.id}")
            return image_record
            
        except Exception as e:
            print(f"❌ Erreur création image mock : {e}")
            return None
